# Clash Configuration File
# Server: ************* (srv461468751.host)

port: 7890
socks-port: 7891
allow-lan: true
mode: rule
log-level: info
external-controller: 127.0.0.1:9090

dns:
  enable: true
  listen: 0.0.0.0:53
  enhanced-mode: fake-ip
  fake-ip-range: **********/16
  nameserver:
    - *******
    - *******
    - ***************
  fallback:
    - tls://*******:853
    - tls://*******:853

proxies:
  # Shadowsocks Proxies
  - name: "SS-Group1"
    type: ss
    server: *************
    port: 8388
    cipher: chacha20-ietf-poly1305
    password: "Group1Pass2025"
    
  - name: "SS-Group2"
    type: ss
    server: *************
    port: 8389
    cipher: chacha20-ietf-poly1305
    password: "Group2Pass2025"
    
  - name: "SS-Group3"
    type: ss
    server: *************
    port: 8390
    cipher: chacha20-ietf-poly1305
    password: "Group3Pass2025"
    
  - name: "SS-Group4"
    type: ss
    server: *************
    port: 8391
    cipher: chacha20-ietf-poly1305
    password: "Group4Pass2025"

  # SOCKS5 Proxies
  - name: "SOCKS5-Group1"
    type: socks5
    server: *************
    port: 1080
    username: socks5user1
    password: "S5Pass2025_1"

  - name: "SOCKS5-Group2"
    type: socks5
    server: *************
    port: 1081
    username: socks5user2
    password: "S5Pass2025_2"

  - name: "SOCKS5-Group3"
    type: socks5
    server: *************
    port: 1082
    username: socks5user3
    password: "S5Pass2025_3"

  - name: "SOCKS5-Group4"
    type: socks5
    server: *************
    port: 1083
    username: socks5user4
    password: "S5Pass2025_4"

  # Load Balanced Proxies
  - name: "SOCKS5-LoadBalance"
    type: socks5
    server: *************
    port: 1090

  - name: "SOCKS5-HighAvailability"
    type: socks5
    server: *************
    port: 1091

  - name: "SS-LoadBalance"
    type: ss
    server: *************
    port: 8400
    cipher: chacha20-ietf-poly1305
    password: "Group1Pass2025"

  - name: "SS-HighAvailability"
    type: ss
    server: *************
    port: 8401
    cipher: chacha20-ietf-poly1305
    password: "Group1Pass2025"

proxy-groups:
  - name: "Proxy"
    type: select
    proxies:
      - "Auto-Select"
      - "Load-Balance"
      - "High-Availability"
      - "SS-Group1"
      - "SS-Group2"
      - "SS-Group3"
      - "SS-Group4"
      - "SOCKS5-Group1"
      - "SOCKS5-Group2"
      - "SOCKS5-Group3"
      - "SOCKS5-Group4"
      - "SS-LoadBalance"
      - "SS-HighAvailability"
      - "SOCKS5-LoadBalance"
      - "SOCKS5-HighAvailability"
      - "DIRECT"

  - name: "Auto-Select"
    type: url-test
    proxies:
      - "SS-Group1"
      - "SS-Group2"
      - "SS-Group3"
      - "SS-Group4"
    url: 'http://www.gstatic.com/generate_204'
    interval: 300

  - name: "Fallback"
    type: fallback
    proxies:
      - "SS-Group1"
      - "SS-Group2"
      - "SS-Group3"
      - "SS-Group4"
    url: 'http://www.gstatic.com/generate_204'
    interval: 300

  - name: "Load-Balance"
    type: load-balance
    proxies:
      - "SS-Group1"
      - "SS-Group2"
      - "SS-Group3"
      - "SS-Group4"
    url: 'http://www.gstatic.com/generate_204'
    interval: 300

rules:
  # Local Area Network
  - DOMAIN-SUFFIX,local,DIRECT
  - IP-CIDR,*********/8,DIRECT
  - IP-CIDR,**********/12,DIRECT
  - IP-CIDR,***********/16,DIRECT
  - IP-CIDR,10.0.0.0/8,DIRECT
  - IP-CIDR,********/8,DIRECT
  - IP-CIDR,**********/10,DIRECT

  # China Domains
  - DOMAIN-SUFFIX,cn,DIRECT
  - DOMAIN-KEYWORD,baidu,DIRECT
  - DOMAIN-KEYWORD,taobao,DIRECT
  - DOMAIN-KEYWORD,qq,DIRECT
  - DOMAIN-KEYWORD,weixin,DIRECT
  - DOMAIN-KEYWORD,alipay,DIRECT

  # Global Services
  - DOMAIN-SUFFIX,google.com,Proxy
  - DOMAIN-SUFFIX,youtube.com,Proxy
  - DOMAIN-SUFFIX,facebook.com,Proxy
  - DOMAIN-SUFFIX,twitter.com,Proxy
  - DOMAIN-SUFFIX,instagram.com,Proxy
  - DOMAIN-SUFFIX,telegram.org,Proxy
  - DOMAIN-SUFFIX,github.com,Proxy
  - DOMAIN-SUFFIX,netflix.com,Proxy
  - DOMAIN-SUFFIX,openai.com,Proxy

  # Final Rule
  - GEOIP,CN,DIRECT
  - MATCH,Proxy
